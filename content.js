// This script runs in the isolated content script world.

const init = () => {
    console.log('X User Info Enhancer: Content script loaded.');

    // 1. Check if the functionality is enabled
    chrome.storage.local.get('isEnabled', (data) => {
        const isEnabled = data.isEnabled === undefined ? true : !!data.isEnabled;
        console.log('X User Info Enhancer: Is enabled?', isEnabled);
        if (isEnabled) {
            // 2. Inject the interceptor script into the page's main world
            const s = document.createElement('script');
            s.src = chrome.runtime.getURL('interceptor.js');
            (document.head || document.documentElement).appendChild(s);
            s.onload = () => s.remove(); // Clean up the script tag after it has run

            // 3. Listen for the data event from the interceptor
            window.addEventListener('XUserInfoData', (event) => {
                console.log('X User Info Enhancer: Received data from interceptor.');
                handleData(event.detail);
            });
        }
    });

    // Reload page if user toggles the switch
    chrome.storage.onChanged.addListener((changes) => {
        if (changes.isEnabled) {
            window.location.reload();
        }
    });
};

const handleData = (data) => {
    const users = new Map();

    // Recursive function to find all user objects
    const findUsers = (obj) => {
        if (!obj || typeof obj !== 'object') return;

        // Check if the current object is a user result
        // A user object has __typename: 'User' and typically a legacy property
        if (obj.__typename === 'User' && (obj.legacy || obj.core)) {
            const userLegacy = obj.legacy;
            const userCore = obj.core;

            // Determine screen_name, which is crucial for the map key and DOM lookup
            const screenName = userLegacy?.screen_name || userCore?.screen_name;

            if (screenName && !users.has(screenName)) {
                // Combine data from legacy and core, prioritizing legacy where applicable
                const extractedData = {
                    "创建时间": userLegacy?.created_at || userCore?.created_at,
                    "昵称": userLegacy?.name || userCore?.name,
                    "用户名": screenName,
                    "关注者": userLegacy?.followers_count ?? userLegacy?.normal_followers_count,
                    "正在关注": userLegacy?.friends_count,
                    "帖子数": userLegacy?.statuses_count
                };
                users.set(screenName, extractedData);
            }
        }

        // Recurse into arrays and objects
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                findUsers(obj[key]);
            }
        }
    };

    findUsers(data);

    if (users.size > 0) {
        console.log("--- ✔️ 提取成功! ✔️ ---");
        console.log("已提取的用户信息:", users);
        // Use a timeout to ensure the DOM has time to render after the API call
        setTimeout(() => injectUserInfo(users), 500);
    } else {
        console.log("--- ❌ 提取失败! ❌ ---");
        console.log("未找到任何用户信息。请检查数据结构或页面内容。");
    }
};

const injectUserInfo = (users) => {
  console.log('X User Info Enhancer: Starting DOM injection.');
  users.forEach((userData, screenName) => {
    const userElements = document.querySelectorAll(`a[href="/${screenName}"][role="link"]`);

    userElements.forEach((userElement) => {
        const nameContainer = userElement.closest('div[data-testid="User-Name"]');
        if (nameContainer && !nameContainer.querySelector('.x-enhancer-container')) {
            const infoContainer = document.createElement('div');
            infoContainer.className = 'x-enhancer-container';

            const createdAt = new Date(userData["创建时间"]);
            const formattedDate = `${createdAt.getFullYear()}-${String(createdAt.getMonth() + 1).padStart(2, '0')}-${String(createdAt.getDate()).padStart(2, '0')}`;
            const followers = userData["关注者"];
            const following = userData["正在关注"];
            const posts = userData["帖子数"];

            infoContainer.innerHTML = `
              <span class="x-enhancer-item">注册于: ${formattedDate}</span>
              <span class="x-enhancer-item">帖子: ${posts.toLocaleString()}</span>
              <span class="x-enhancer-item">正在关注: ${following.toLocaleString()}</span>
              <span class="x-enhancer-item">关注者: ${followers.toLocaleString()}</span>
            `;
            nameContainer.appendChild(infoContainer);
        }
    });
  });
};

init();
