
(function() {
    console.log('X Enhancer Interceptor: <PERSON>ript injected and running in page context.');

    const handleData = (data) => {
        // Send the data back to the content script via a custom event
        window.dispatchEvent(new CustomEvent('XUserInfoData', { detail: data }));
    };

    // Override XMLHttpRequest
    const originalXhrOpen = XMLHttpRequest.prototype.open;
    const originalXhrSend = XMLHttpRequest.prototype.send;

    XMLHttpRequest.prototype.open = function(...args) {
        this._url = args[1];
        return originalXhrOpen.apply(this, args);
    };

    XMLHttpRequest.prototype.send = function(...args) {
        this.addEventListener('load', () => {
            if (this._url && this._url.includes('/i/api/graphql/') && this._url.includes('/TweetDetail')) {
                console.log('[XHR Interceptor] Matched TweetDetail API call:', this._url);
                try {
                    const data = JSON.parse(this.responseText);
                    handleData(data);
                } catch (e) {}
            }
        });
        return originalXhrSend.apply(this, args);
    };

    // Override fetch
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
        const resource = args[0];
        const url = resource instanceof Request ? resource.url : String(resource);

        const response = await originalFetch(...args);

        if (url.includes('/i/api/graphql/') && url.includes('/TweetDetail')) {
            console.log('[Fetch Interceptor] Matched TweetDetail API call:', url);
            const clonedResponse = response.clone();
            clonedResponse.json().then(data => {
                handleData(data);
            });
        }
        return response;
    };
})();
